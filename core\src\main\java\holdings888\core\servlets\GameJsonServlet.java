package holdings888.core.servlets;

import com.fasterxml.jackson.databind.ObjectMapper;
import holdings888.core.models.GameJsonModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.sling.api.SlingHttpServletRequest;
import org.apache.sling.api.SlingHttpServletResponse;
import org.apache.sling.api.servlets.HttpConstants;
import org.apache.sling.api.servlets.SlingSafeMethodsServlet;
import org.apache.sling.servlets.annotations.SlingServletResourceTypes;
import org.osgi.service.component.annotations.Component;
import org.osgi.service.component.propertytypes.ServiceDescription;

import javax.servlet.Servlet;
import java.io.IOException;
import java.util.List;
import java.util.Map;

import static holdings888.core.utils.Constants.APPLICATION_JSON;

@Slf4j
@Component(service = { Servlet.class })
@SlingServletResourceTypes(
    resourceTypes = {
        "holdings888/components/common888/editorial/game-json",
        "holdings888/components/editorial/game-json",
        "holdings888/components/mrgreen/editorial/game-json", 
        "holdings888/components/seven/editorial/game-json"
    }, 
    methods = HttpConstants.METHOD_GET, 
    extensions = "json"
)
@ServiceDescription("Game JSON Component Servlet")
public class GameJsonServlet extends SlingSafeMethodsServlet {

    private static final long serialVersionUID = 1L;

    @Override
    protected void doGet(SlingHttpServletRequest request, SlingHttpServletResponse response) throws IOException {
        response.setContentType(APPLICATION_JSON);
        response.setCharacterEncoding("UTF-8");

        try {
            GameJsonModel gameModel = request.adaptTo(GameJsonModel.class);
            
            if (gameModel != null) {
                List<Map<String, Object>> games = gameModel.getGamesAsMapList();
                
                ObjectMapper objectMapper = new ObjectMapper();
                String jsonOutput = objectMapper.writeValueAsString(games);
                
                response.getWriter().write(jsonOutput);
                response.setStatus(200);
            } else {
                log.warn("GameJsonModel could not be adapted from request");
                response.getWriter().write("[]");
                response.setStatus(200);
            }
        } catch (Exception e) {
            log.error("Error generating JSON response for game component", e);
            response.getWriter().write("[]");
            response.setStatus(500);
        }
    }
}
