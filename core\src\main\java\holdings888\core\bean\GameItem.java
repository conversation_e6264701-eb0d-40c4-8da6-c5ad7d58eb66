package holdings888.core.bean;

import lombok.Data;
import org.apache.sling.api.resource.Resource;
import org.apache.sling.models.annotations.DefaultInjectionStrategy;
import org.apache.sling.models.annotations.Model;

import javax.inject.Inject;

@Data
@Model(adaptables = Resource.class, defaultInjectionStrategy = DefaultInjectionStrategy.OPTIONAL)
public class GameItem {

    @Inject
    private Integer gameId;

    @Inject
    private String gameName;

    @Inject
    private String imagePath;
}
