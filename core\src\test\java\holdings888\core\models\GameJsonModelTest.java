package holdings888.core.models;

import holdings888.core.bean.GameItem;
import io.wcm.testing.mock.aem.junit5.AemContext;
import io.wcm.testing.mock.aem.junit5.AemContextExtension;
import org.apache.sling.api.resource.Resource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for GameJsonModel
 */
@ExtendWith(AemContextExtension.class)
public class GameJsonModelTest {

    private final AemContext ctx = new AemContext();
    private GameJsonModel gameJsonModel;

    @BeforeEach
    void setUp() {
        ctx.addModelsForClasses(GameJsonModel.class, GameItem.class);
        
        // Create test content
        ctx.create().resource("/content/test/jcr:content/game-json",
            "sling:resourceType", "holdings888/components/common888/editorial/game-json");
        
        ctx.create().resource("/content/test/jcr:content/game-json/games");
        ctx.create().resource("/content/test/jcr:content/game-json/games/item0",
            "gameId", 1234,
            "gameName", "Test Game 1",
            "imagePath", "https://example.com/game1.gif");
        ctx.create().resource("/content/test/jcr:content/game-json/games/item1",
            "gameId", 5678,
            "gameName", "Test Game 2", 
            "imagePath", "https://example.com/game2.gif");

        ctx.currentResource("/content/test/jcr:content/game-json");
        gameJsonModel = ctx.request().adaptTo(GameJsonModel.class);
    }

    @Test
    void testModelInitialization() {
        assertNotNull(gameJsonModel);
        assertNotNull(gameJsonModel.getGames());
        assertEquals(2, gameJsonModel.getGames().size());
    }

    @Test
    void testGameData() {
        List<GameItem> games = gameJsonModel.getGames();
        
        GameItem game1 = games.get(0);
        assertEquals(Integer.valueOf(1234), game1.getGameId());
        assertEquals("Test Game 1", game1.getGameName());
        assertEquals("https://example.com/game1.gif", game1.getImagePath());
        
        GameItem game2 = games.get(1);
        assertEquals(Integer.valueOf(5678), game2.getGameId());
        assertEquals("Test Game 2", game2.getGameName());
        assertEquals("https://example.com/game2.gif", game2.getImagePath());
    }

    @Test
    void testJsonOutput() {
        String jsonOutput = gameJsonModel.getJsonOutput();
        assertNotNull(jsonOutput);
        assertTrue(jsonOutput.contains("game_id"));
        assertTrue(jsonOutput.contains("game_name"));
        assertTrue(jsonOutput.contains("image_path"));
        assertTrue(jsonOutput.contains("1234"));
        assertTrue(jsonOutput.contains("Test Game 1"));
    }

    @Test
    void testGamesAsMapList() {
        List<Map<String, Object>> gamesList = gameJsonModel.getGamesAsMapList();
        assertNotNull(gamesList);
        assertEquals(2, gamesList.size());
        
        Map<String, Object> game1 = gamesList.get(0);
        assertEquals(1234, game1.get("game_id"));
        assertEquals("Test Game 1", game1.get("game_name"));
        assertEquals("https://example.com/game1.gif", game1.get("image_path"));
    }
}
