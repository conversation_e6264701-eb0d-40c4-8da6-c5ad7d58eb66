package holdings888.core.models;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import holdings888.core.bean.GameItem;
import lombok.Getter;
import org.apache.sling.api.SlingHttpServletRequest;
import org.apache.sling.models.annotations.Model;
import org.apache.sling.models.annotations.Optional;
import org.apache.sling.models.annotations.injectorspecific.ChildResource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Model(adaptables = SlingHttpServletRequest.class)
public class GameJsonModel {

    private final Logger logger = LoggerFactory.getLogger(GameJsonModel.class);

    @Optional
    @ChildResource(name = "games")
    @Getter
    private List<GameItem> games;

    @Getter
    private String jsonOutput;

    @PostConstruct
    protected void init() {
        generateJsonOutput();
    }

    private void generateJsonOutput() {
        if (games != null && !games.isEmpty()) {
            List<Map<String, Object>> gameList = new ArrayList<>();
            
            for (GameItem game : games) {
                Map<String, Object> gameMap = new HashMap<>();
                gameMap.put("game_id", game.getGameId());
                gameMap.put("game_name", game.getGameName());
                gameMap.put("image_path", game.getImagePath());
                gameList.add(gameMap);
            }

            ObjectMapper objectMapper = new ObjectMapper();
            try {
                jsonOutput = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(gameList);
            } catch (JsonProcessingException e) {
                logger.error("Error generating JSON output for games", e);
                jsonOutput = "[]";
            }
        } else {
            jsonOutput = "[]";
        }
    }

    public List<Map<String, Object>> getGamesAsMapList() {
        List<Map<String, Object>> gameList = new ArrayList<>();
        
        if (games != null) {
            for (GameItem game : games) {
                Map<String, Object> gameMap = new HashMap<>();
                gameMap.put("game_id", game.getGameId());
                gameMap.put("game_name", game.getGameName());
                gameMap.put("image_path", game.getImagePath());
                gameList.add(gameMap);
            }
        }
        
        return gameList;
    }
}
