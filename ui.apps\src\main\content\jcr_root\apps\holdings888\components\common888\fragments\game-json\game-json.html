<sly data-sly-use.gameModel="holdings888.core.models.GameJsonModel"/>
<sly data-sly-use.template="core/wcm/components/commons/v1/templates.html"/>
<sly data-sly-test.hasContent="${gameModel.games && gameModel.games.size > 0}"/>
<sly data-sly-call="${template.placeholder @ isEmpty=!hasContent}"/>

<sly data-sly-test="${wcmmode.edit}">
    <div class="cmp-game-json" style="border: 2px dashed #ccc; padding: 20px; margin: 10px; background-color: #f9f9f9;">
        <h4 style="margin-top: 0; color: #333;">🎮 Game JSON Component</h4>
        <sly data-sly-test="${hasContent}">
            <p><strong>Status:</strong> ✅ Configured with ${gameModel.games.size} game(s)</p>
            <p><strong>JSON Endpoint:</strong> <code>${request.requestURL}.json</code></p>
            <div class="cmp-game-json__games" style="margin: 15px 0;">
                <h5>Games:</h5>
                <ul style="margin: 0; padding-left: 20px;">
                    <sly data-sly-list="${gameModel.games}">
                        <li>ID: ${item.gameId}, Name: "${item.gameName}", Image: ${item.imagePath}</li>
                    </sly>
                </ul>
            </div>
            <details style="margin-top: 15px;">
                <summary style="cursor: pointer; font-weight: bold;">📄 JSON Preview</summary>
                <pre style="background: #fff; padding: 10px; border: 1px solid #ddd; overflow-x: auto; font-size: 12px;">${gameModel.jsonOutput @ context='text'}</pre>
            </details>
        </sly>
        <sly data-sly-test="${!hasContent}">
            <p><strong>Status:</strong> ⚠️ No games configured</p>
            <p>Please configure games in the component dialog to generate JSON output.</p>
        </sly>
    </div>
</sly>

<!-- In publish mode, this component outputs nothing in HTML -->
