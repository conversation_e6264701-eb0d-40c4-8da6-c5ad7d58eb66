<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:sling="http://sling.apache.org/jcr/sling/1.0" xmlns:granite="http://www.adobe.com/jcr/granite/1.0" xmlns:cq="http://www.day.com/jcr/cq/1.0" xmlns:jcr="http://www.jcp.org/jcr/1.0" xmlns:nt="http://www.jcp.org/jcr/nt/1.0"
    jcr:primaryType="nt:unstructured"
    sling:resourceType="cq/gui/components/authoring/dialog"
    extraClientlibs="[holdings888.multifield]"
    helpPath="https://www.adobe.com/go/aem_cmp_title_v2"
    title="Game JSON Component">
    <content
        jcr:primaryType="nt:unstructured"
        sling:resourceType="granite/ui/components/coral/foundation/container">
        <items jcr:primaryType="nt:unstructured">
            <tabs
                jcr:primaryType="nt:unstructured"
                sling:resourceType="granite/ui/components/coral/foundation/tabs"
                maximized="{Boolean}true">
                <items jcr:primaryType="nt:unstructured">
                    <games
                        jcr:primaryType="nt:unstructured"
                        jcr:title="Games"
                        sling:resourceType="granite/ui/components/coral/foundation/container"
                        margin="{Boolean}true">
                        <items jcr:primaryType="nt:unstructured">
                            <columns
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/fixedcolumns"
                                margin="{Boolean}true">
                                <items jcr:primaryType="nt:unstructured">
                                    <column
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/container">
                                        <items jcr:primaryType="nt:unstructured">
                                            <gameItems
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/multifield"
                                                composite="{Boolean}true"
                                                validation="minmax-multifield"
                                                fieldLabel="Game Items">
                                                <granite:data
                                                    jcr:primaryType="nt:unstructured"
                                                    min-items="1"
                                                    name="games"/>
                                                <field
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/container"
                                                    name="./games">
                                                    <items jcr:primaryType="nt:unstructured">
                                                        <gameId
                                                            jcr:primaryType="nt:unstructured"
                                                            sling:resourceType="granite/ui/components/coral/foundation/form/numberfield"
                                                            fieldLabel="Game ID"
                                                            name="./gameId"
                                                            required="{Boolean}true"
                                                            min="1"
                                                            step="1"/>
                                                        <gameName
                                                            jcr:primaryType="nt:unstructured"
                                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                            fieldLabel="Game Name"
                                                            name="./gameName"
                                                            required="{Boolean}true"/>
                                                        <imagePath
                                                            jcr:primaryType="nt:unstructured"
                                                            sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                            fieldLabel="Image Path"
                                                            name="./imagePath"
                                                            required="{Boolean}true"/>
                                                    </items>
                                                </field>
                                            </gameItems>
                                        </items>
                                    </column>
                                </items>
                            </columns>
                        </items>
                    </games>
                </items>
            </tabs>
        </items>
    </content>
</jcr:root>
